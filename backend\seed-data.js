const mongoose = require('mongoose');
const Product = require('./models/Product');
const Debt = require('./models/Debt');
require('dotenv').config();

const sampleProducts = [
  {
    name: 'Lucky Me Instant Noodles',
    netWeight: '70g',
    price: 15.00,
    stockQuantity: 50,
    category: 'snacks',
    image: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=300'
  },
  {
    name: 'Coca-Cola 350ml',
    netWeight: '350ml',
    price: 25.00,
    stockQuantity: 30,
    category: 'beverages',
    image: 'https://images.unsplash.com/photo-1554866585-cd94860890b7?w=300'
  },
  {
    name: 'Corned Beef 150g',
    netWeight: '150g',
    price: 45.00,
    stockQuantity: 20,
    category: 'canned goods',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300'
  },
  {
    name: 'Pantene Shampoo',
    netWeight: '170ml',
    price: 85.00,
    stockQuantity: 15,
    category: 'personal care',
    image: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=300'
  },
  {
    name: 'Tide Detergent Powder',
    netWeight: '1kg',
    price: 120.00,
    stockQuantity: 8,
    category: 'household items',
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300'
  },
  {
    name: 'Maggi Magic Sarap',
    netWeight: '50g',
    price: 12.00,
    stockQuantity: 25,
    category: 'condiments',
    image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300'
  },
  {
    name: 'Bear Brand Milk',
    netWeight: '300ml',
    price: 35.00,
    stockQuantity: 18,
    category: 'dairy',
    image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=300'
  },
  {
    name: 'Skyflakes Crackers',
    netWeight: '250g',
    price: 28.00,
    stockQuantity: 12,
    category: 'snacks',
    image: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=300'
  },
  {
    name: 'Gardenia Bread',
    netWeight: '450g',
    price: 55.00,
    stockQuantity: 5,
    category: 'bread & pastries',
    image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=300'
  },
  {
    name: 'Kopiko Coffee',
    netWeight: '30g',
    price: 8.00,
    stockQuantity: 40,
    category: 'beverages',
    image: 'https://images.unsplash.com/photo-1497935586351-b67a49e012bf?w=300'
  }
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Clear existing data
    await Product.deleteMany({});
    await Debt.deleteMany({});
    console.log('🗑️  Cleared existing data');

    // Insert sample products
    const products = await Product.insertMany(sampleProducts);
    console.log(`📦 Created ${products.length} sample products`);

    // Create sample debt records
    const sampleDebts = [
      {
        customerName: 'Maria Santos',
        productName: products[0].name,
        productId: products[0]._id,
        priceAtTimeOfDebt: products[0].price,
        quantity: 2,
        dateOfDebt: new Date('2024-01-15'),
        isPaid: false,
        notes: 'Regular customer'
      },
      {
        customerName: 'Juan Dela Cruz',
        productName: products[1].name,
        productId: products[1]._id,
        priceAtTimeOfDebt: products[1].price,
        quantity: 1,
        dateOfDebt: new Date('2024-01-18'),
        isPaid: true,
        notes: 'Paid in cash'
      },
      {
        customerName: 'Maria Santos',
        productName: products[2].name,
        productId: products[2]._id,
        priceAtTimeOfDebt: products[2].price,
        quantity: 1,
        dateOfDebt: new Date('2024-01-20'),
        isPaid: false,
        notes: 'Will pay next week'
      },
      {
        customerName: 'Pedro Garcia',
        productName: products[3].name,
        productId: products[3]._id,
        priceAtTimeOfDebt: products[3].price,
        quantity: 1,
        dateOfDebt: new Date('2024-01-22'),
        isPaid: false,
        notes: 'Emergency purchase'
      },
      {
        customerName: 'Ana Reyes',
        productName: products[4].name,
        productId: products[4]._id,
        priceAtTimeOfDebt: products[4].price,
        quantity: 1,
        dateOfDebt: new Date('2024-01-25'),
        isPaid: true,
        notes: 'Paid immediately'
      }
    ];

    const debts = await Debt.insertMany(sampleDebts);
    console.log(`💳 Created ${debts.length} sample debt records`);

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Products: ${products.length}`);
    console.log(`   Debt Records: ${debts.length}`);
    console.log(`   Customers: ${[...new Set(sampleDebts.map(d => d.customerName))].length}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedDatabase();
