const mongoose = require('mongoose');

const debtSchema = new mongoose.Schema({
  customerName: {
    type: String,
    required: [true, 'Customer name is required'],
    trim: true,
    maxlength: [100, 'Customer name cannot exceed 100 characters']
  },
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true
  },
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, 'Product ID is required']
  },
  priceAtTimeOfDebt: {
    type: Number,
    required: [true, 'Price at time of debt is required'],
    min: [0, 'Price cannot be negative']
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be at least 1']
  },
  totalAmount: {
    type: Number,
    required: true
  },
  dateOfDebt: {
    type: Date,
    required: [true, 'Date of debt is required'],
    default: Date.now
  },
  isPaid: {
    type: Boolean,
    default: false
  },
  datePaid: {
    type: Date,
    default: null
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot exceed 500 characters'],
    default: ''
  }
}, {
  timestamps: true
});

// Calculate total amount before saving
debtSchema.pre('save', function(next) {
  this.totalAmount = this.priceAtTimeOfDebt * this.quantity;
  next();
});

// Update datePaid when isPaid changes to true
debtSchema.pre('save', function(next) {
  if (this.isModified('isPaid') && this.isPaid && !this.datePaid) {
    this.datePaid = new Date();
  } else if (this.isModified('isPaid') && !this.isPaid) {
    this.datePaid = null;
  }
  next();
});

// Indexes for better query performance
debtSchema.index({ customerName: 1, isPaid: 1 });
debtSchema.index({ dateOfDebt: -1 });
debtSchema.index({ productId: 1 });

// Virtual for customer debt summary
debtSchema.virtual('customerDebtSummary', {
  ref: 'Debt',
  localField: 'customerName',
  foreignField: 'customerName'
});

module.exports = mongoose.model('Debt', debtSchema);
