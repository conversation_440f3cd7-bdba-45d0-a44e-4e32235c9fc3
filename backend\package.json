{"name": "sari-sari-store-backend", "version": "1.0.0", "description": "Backend API for Sari-Sari Store Admin Dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seed-data.js", "test-connection": "node test-connection.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["sari-sari", "store", "inventory", "debt", "management"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2"}}