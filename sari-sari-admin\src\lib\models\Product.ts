import mongoose, { Schema, Document } from 'mongoose';
import { Product as IProduct } from '@/types';

export interface ProductDocument extends IProduct, Document {}

const ProductSchema = new Schema<ProductDocument>({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters']
  },
  image: {
    type: String,
    trim: true,
    default: ''
  },
  netWeight: {
    type: String,
    required: [true, 'Net weight is required'],
    trim: true,
    maxlength: [50, 'Net weight cannot exceed 50 characters']
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative'],
    validate: {
      validator: function(value: number) {
        return value >= 0;
      },
      message: 'Price must be a positive number'
    }
  },
  stockQuantity: {
    type: Number,
    required: [true, 'Stock quantity is required'],
    min: [0, 'Stock quantity cannot be negative'],
    default: 0
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: {
      values: [
        'snacks',
        'canned goods',
        'beverages',
        'personal care',
        'household',
        'condiments',
        'instant foods',
        'dairy',
        'frozen',
        'others'
      ],
      message: 'Invalid category'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
ProductSchema.index({ name: 1 });
ProductSchema.index({ category: 1 });
ProductSchema.index({ stockQuantity: 1 });

// Virtual for low stock indicator
ProductSchema.virtual('isLowStock').get(function() {
  return this.stockQuantity <= 5;
});

// Pre-save middleware to ensure data consistency
ProductSchema.pre('save', function(next) {
  if (this.price < 0) {
    this.price = 0;
  }
  if (this.stockQuantity < 0) {
    this.stockQuantity = 0;
  }
  next();
});

export default mongoose.models.Product || mongoose.model<ProductDocument>('Product', ProductSchema);
