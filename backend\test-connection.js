const mongoose = require('mongoose');
require('dotenv').config();

console.log('Testing MongoDB connection...');
console.log('MongoDB URI:', process.env.MONGODB_URI);

mongoose.connect(process.env.MONGODB_URI)
  .then(() => {
    console.log('✅ Successfully connected to MongoDB');
    process.exit(0);
  })
  .catch((error) => {
    console.log('❌ Failed to connect to MongoDB');
    console.log('Error:', error.message);
    console.log('\n📝 Setup Instructions:');
    console.log('1. Install MongoDB locally or use MongoDB Atlas (cloud)');
    console.log('2. For local MongoDB: Download from https://www.mongodb.com/try/download/community');
    console.log('3. For MongoDB Atlas: Create free account at https://www.mongodb.com/atlas');
    console.log('4. Update MONGODB_URI in .env file with your connection string');
    console.log('\nExample connection strings:');
    console.log('Local: mongodb://localhost:27017/sari-sari-store');
    console.log('Atlas: mongodb+srv://username:<EMAIL>/sari-sari-store');
    process.exit(1);
  });
