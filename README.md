# Sari-Sari Store Admin Dashboard

A comprehensive web-based admin dashboard for managing a sari-sari store's inventory and customer debt records.

## Features

### Product Inventory Management
- Add, edit, and delete products
- Product details: name, image, net weight, price, stock quantity, category
- Image upload support
- Category-based organization

### Customer Debt (Utang) Management
- Track customer debts with detailed records
- Debt details: customer name, product, price at time of debt, quantity, date
- Paid/Unpaid status tracking
- View debts grouped by customer
- Calculate total debt per customer

## Tech Stack

- **Backend**: Node.js + Express + MongoDB
- **Frontend**: React + Vite
- **Database**: MongoDB with Mongoose ODM
- **Styling**: CSS3 with responsive design

## Project Structure

```
├── backend/          # Express API server
│   ├── models/       # MongoDB schemas
│   ├── routes/       # API routes
│   ├── middleware/   # Custom middleware
│   └── uploads/      # Image uploads
├── frontend/         # React application
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── services/
└── README.md
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local installation or MongoDB Atlas cloud account)

### Installation

1. **Clone or download the project files**

2. **Install backend dependencies:**
   ```bash
   cd backend
   npm install
   ```

3. **Install frontend dependencies:**
   ```bash
   cd frontend
   npm install
   ```

4. **Set up MongoDB:**

   **Option A: Local MongoDB Installation**
   - Download and install MongoDB Community Server from [https://www.mongodb.com/try/download/community](https://www.mongodb.com/try/download/community)
   - Start MongoDB service:
     - Windows: MongoDB should start automatically as a service
     - macOS: `brew services start mongodb/brew/mongodb-community`
     - Linux: `sudo systemctl start mongod`

   **Option B: MongoDB Atlas (Cloud - Recommended)**
   - Create a free account at [https://www.mongodb.com/atlas](https://www.mongodb.com/atlas)
   - Create a new cluster (free tier available)
   - Get your connection string from the "Connect" button
   - Update the `.env` file with your Atlas connection string

5. **Configure environment variables:**
   - Copy `backend/.env.example` to `backend/.env`
   - Update the `MONGODB_URI` in `backend/.env`:
     ```
     # For local MongoDB:
     MONGODB_URI=mongodb://localhost:27017/sari-sari-store

     # For MongoDB Atlas:
     MONGODB_URI=mongodb+srv://username:<EMAIL>/sari-sari-store
     ```

6. **Test MongoDB connection:**
   ```bash
   cd backend
   npm run test-connection
   ```

7. **Seed the database with sample data (optional):**
   ```bash
   cd backend
   npm run seed
   ```

8. **Start the development servers:**
   ```bash
   # Terminal 1: Start backend (from backend directory)
   npm run dev

   # Terminal 2: Start frontend (from frontend directory)
   npm run dev
   ```

8. **Access the application:**
   - Frontend: [http://localhost:3000](http://localhost:3000)
   - Backend API: [http://localhost:5000](http://localhost:5000)

### Troubleshooting

**MongoDB Connection Issues:**
- Ensure MongoDB is running (local) or connection string is correct (Atlas)
- Check firewall settings for local MongoDB
- For Atlas: Ensure your IP is whitelisted and credentials are correct

**Port Conflicts:**
- Backend runs on port 5000, frontend on port 3000
- Change ports in `.env` (backend) or `vite.config.js` (frontend) if needed

**Permission Issues (Windows):**
- Run PowerShell as Administrator
- Or use: `powershell -ExecutionPolicy Bypass -Command "npm install"`

## API Endpoints

### Products
- `GET /api/products` - Get all products
- `POST /api/products` - Create new product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

### Debts
- `GET /api/debts` - Get all debts
- `POST /api/debts` - Create new debt record
- `PUT /api/debts/:id` - Update debt record
- `DELETE /api/debts/:id` - Delete debt record
- `GET /api/debts/customer/:name` - Get debts by customer

## License

MIT License
