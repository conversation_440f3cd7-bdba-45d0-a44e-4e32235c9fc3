# Sari-Sari Store Admin Dashboard

A comprehensive web-based admin dashboard for managing a sari-sari store's inventory and customer debt records.

## Features

### Product Inventory Management
- Add, edit, and delete products
- Product details: name, image, net weight, price, stock quantity, category
- Image upload support
- Category-based organization

### Customer Debt (Utang) Management
- Track customer debts with detailed records
- Debt details: customer name, product, price at time of debt, quantity, date
- Paid/Unpaid status tracking
- View debts grouped by customer
- Calculate total debt per customer

## Tech Stack

- **Backend**: Node.js + Express + MongoDB
- **Frontend**: React + Vite
- **Database**: MongoDB with Mongoose ODM
- **Styling**: CSS3 with responsive design

## Project Structure

```
├── backend/          # Express API server
│   ├── models/       # MongoDB schemas
│   ├── routes/       # API routes
│   ├── middleware/   # Custom middleware
│   └── uploads/      # Image uploads
├── frontend/         # React application
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── services/
└── README.md
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud instance)

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```bash
   cd backend
   npm install
   ```

3. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

4. Set up environment variables (see .env.example files)

5. Start the development servers:
   ```bash
   # Backend (from backend directory)
   npm run dev

   # Frontend (from frontend directory)
   npm run dev
   ```

## API Endpoints

### Products
- `GET /api/products` - Get all products
- `POST /api/products` - Create new product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

### Debts
- `GET /api/debts` - Get all debts
- `POST /api/debts` - Create new debt record
- `PUT /api/debts/:id` - Update debt record
- `DELETE /api/debts/:id` - Delete debt record
- `GET /api/debts/customer/:name` - Get debts by customer

## License

MIT License
