import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import CustomerDebt from '@/lib/models/CustomerDebt';

// GET /api/debts/summary - Get debt summary grouped by customer
export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const customerName = searchParams.get('customer');

    if (customerName) {
      // Get summary for specific customer
      const summary = await CustomerDebt.getDebtSummaryByCustomer(customerName);
      return NextResponse.json({
        success: true,
        data: summary
      });
    } else {
      // Get summary for all customers
      const pipeline = [
        {
          $group: {
            _id: '$customerName',
            totalDebt: { $sum: '$totalAmount' },
            totalUnpaid: {
              $sum: {
                $cond: [{ $eq: ['$isPaid', false] }, '$totalAmount', 0]
              }
            },
            debtCount: { $sum: 1 },
            unpaidCount: {
              $sum: {
                $cond: [{ $eq: ['$isPaid', false] }, 1, 0]
              }
            },
            lastDebtDate: { $max: '$dateOfDebt' }
          }
        },
        {
          $project: {
            customerName: '$_id',
            totalDebt: 1,
            totalUnpaid: 1,
            debtCount: 1,
            unpaidCount: 1,
            lastDebtDate: 1,
            _id: 0
          }
        },
        {
          $sort: { totalUnpaid: -1 }
        }
      ];

      const summaries = await CustomerDebt.aggregate(pipeline);

      // Get overall statistics
      const overallStats = await CustomerDebt.aggregate([
        {
          $group: {
            _id: null,
            totalCustomers: { $addToSet: '$customerName' },
            totalDebts: { $sum: 1 },
            totalUnpaidDebts: {
              $sum: {
                $cond: [{ $eq: ['$isPaid', false] }, 1, 0]
              }
            },
            totalDebtAmount: { $sum: '$totalAmount' },
            totalUnpaidAmount: {
              $sum: {
                $cond: [{ $eq: ['$isPaid', false] }, '$totalAmount', 0]
              }
            }
          }
        },
        {
          $project: {
            totalCustomers: { $size: '$totalCustomers' },
            totalDebts: 1,
            totalUnpaidDebts: 1,
            totalDebtAmount: 1,
            totalUnpaidAmount: 1,
            _id: 0
          }
        }
      ]);

      return NextResponse.json({
        success: true,
        data: {
          customerSummaries: summaries,
          overallStats: overallStats[0] || {
            totalCustomers: 0,
            totalDebts: 0,
            totalUnpaidDebts: 0,
            totalDebtAmount: 0,
            totalUnpaidAmount: 0
          }
        }
      });
    }
  } catch (error) {
    console.error('Error fetching debt summary:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch debt summary' },
      { status: 500 }
    );
  }
}
