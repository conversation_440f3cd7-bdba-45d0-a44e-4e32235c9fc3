import React from 'react'
import { Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Products from './pages/Products'
import Debts from './pages/Debts'
import CustomerDebts from './pages/CustomerDebts'
import './App.css'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/products" element={<Products />} />
        <Route path="/debts" element={<Debts />} />
        <Route path="/customer-debts" element={<CustomerDebts />} />
      </Routes>
    </Layout>
  )
}

export default App
