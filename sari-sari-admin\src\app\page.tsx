import Link from 'next/link'
import { Package, Users, BarChart3, ShoppingCart } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Sari-Sari Store Admin Dashboard
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Manage your store inventory, track customer debts, and monitor your business performance
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <Package className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Products</h3>
            <p className="text-gray-600 text-sm">Manage inventory</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <Users className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Customer Debts</h3>
            <p className="text-gray-600 text-sm">Track utang</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <BarChart3 className="w-12 h-12 text-purple-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Analytics</h3>
            <p className="text-gray-600 text-sm">View reports</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <ShoppingCart className="w-12 h-12 text-orange-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Sales</h3>
            <p className="text-gray-600 text-sm">Track transactions</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Link href="/products" className="block">
            <div className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow duration-300 border-l-4 border-blue-500">
              <div className="flex items-center mb-4">
                <Package className="w-8 h-8 text-blue-600 mr-3" />
                <h2 className="text-2xl font-bold text-gray-900">Product Management</h2>
              </div>
              <p className="text-gray-600 mb-4">
                Add, edit, and manage your store inventory. Track stock levels, prices, and product categories.
              </p>
              <div className="text-blue-600 font-semibold">Manage Products →</div>
            </div>
          </Link>

          <Link href="/debts" className="block">
            <div className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow duration-300 border-l-4 border-green-500">
              <div className="flex items-center mb-4">
                <Users className="w-8 h-8 text-green-600 mr-3" />
                <h2 className="text-2xl font-bold text-gray-900">Customer Debts</h2>
              </div>
              <p className="text-gray-600 mb-4">
                Track customer debts (utang), manage payment records, and view outstanding balances.
              </p>
              <div className="text-green-600 font-semibold">Manage Debts →</div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}
