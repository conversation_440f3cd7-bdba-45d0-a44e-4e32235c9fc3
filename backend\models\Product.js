const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters']
  },
  image: {
    type: String,
    default: null // Can be URL or file path
  },
  netWeight: {
    type: String,
    required: [true, 'Net weight is required'],
    trim: true,
    maxlength: [50, 'Net weight cannot exceed 50 characters']
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  stockQuantity: {
    type: Number,
    required: [true, 'Stock quantity is required'],
    min: [0, 'Stock quantity cannot be negative'],
    default: 0
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: {
      values: [
        'snacks',
        'canned goods',
        'beverages',
        'personal care',
        'household items',
        'condiments',
        'dairy',
        'frozen goods',
        'bread & pastries',
        'others'
      ],
      message: 'Please select a valid category'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better search performance
productSchema.index({ name: 'text', category: 1 });
productSchema.index({ category: 1, isActive: 1 });

module.exports = mongoose.model('Product', productSchema);
